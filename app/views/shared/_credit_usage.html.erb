<!-- Credit Usage Section -->
<div class="p-4 bg-gradient-to-br from-white via-purple-50/30 to-pink-50/30 dark:from-gray-800 dark:via-gray-800/80 dark:to-gray-800/80 rounded-xl shadow-lg border border-purple-200/20 dark:border-gray-700/50 backdrop-blur-sm">
  <!-- Plan Badge -->
  <div class="flex items-center justify-between mb-3">
    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits left</p>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm transition-all duration-200 hover:scale-105 <%= Current.user.plan_type == 'pro' ? 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/50 dark:to-pink-900/50 dark:text-purple-300' : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300' %>">
      <%= Current.user.plan_type.capitalize %>
    </span>
  </div>
  <!-- Credits Display -->
  <div class="font-semibold text-gray-900 dark:text-white mb-2">
    <%= number_with_delimiter(Current.user.spendable_credits) %> of <%= number_with_delimiter(Current.user.plan_credits_limit) %> credits
  </div>
  <!-- Generation Estimate -->
  <div class="text-sm text-gray-600 dark:text-gray-400 mb-3 flex items-center">
    <%= flowbite_icon('music-outline', class: 'size-4 mr-1 text-purple-500') %>
    <span class="font-medium"><%= format_generation_estimate(Current.user) %></span>
  </div>
  <!-- Progress Bar -->
  <% usage_percentage = Current.user.plan_credits_limit > 0 ? (Current.user.plan_credits_used.to_f / Current.user.plan_credits_limit * 100) : 0 %>
  <% remaining_percentage = 100 - usage_percentage %>
  <div class="w-full bg-gray-200 rounded-full h-3 mb-4 dark:bg-gray-700 shadow-inner">
    <div class="<%= remaining_percentage > 25 ? 'bg-gradient-to-r from-green-400 to-emerald-500' : remaining_percentage > 10 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gradient-to-r from-red-400 to-red-600' %> h-3 rounded-full transition-all duration-500 shadow-sm" style="width: <%= remaining_percentage %>%"></div>
  </div>
  <!-- Action Button -->
  <% if Current.user.pro? %>
    <!-- Buy More Credits Button (only show for pro users with low credits) -->
    <% if remaining_percentage <= 25 %>
      <%= link_to plans_path, class: "inline-flex items-center justify-center w-full py-3 px-5 text-sm font-semibold text-white bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-xl border-0 hover:from-purple-600 hover:via-pink-600 hover:to-red-600 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl" do %>
        <%= flowbite_icon('credit-card-outline', class: 'mr-2 size-4') %>
        Buy More Credits
      <% end %>
    <% end %>
  <% else %>
    <!-- Upgrade Button (only show for non-pro users) -->
    <%= link_to plans_path, class: "inline-flex items-center justify-center w-full py-3 px-5 text-sm font-semibold text-white bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-xl border-0 hover:from-purple-600 hover:via-pink-600 hover:to-red-600 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl" do %>
      <%= flowbite_icon('arrow-up-right-from-square-outline', class: 'mr-2 size-4') %>
      Upgrade to Pro
    <% end %>
  <% end %>
</div>