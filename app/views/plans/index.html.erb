<% content_for :title, "Plans & Pricing - Musicfy Me" %>
<div class="bg-white dark:bg-gray-900">
  <!-- Hero Section -->
  <section class="bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-screen-xl text-center lg:py-16 lg:px-12">
      <h1 class="mb-4 text-4xl font-extrabold tracking-tight leading-none text-gray-900 md:text-5xl lg:text-6xl dark:text-white">
        Plans & Pricing
      </h1>
      <p class="mb-8 text-lg font-normal text-gray-500 lg:text-xl sm:px-16 xl:px-48 dark:text-gray-400">
        Choose the perfect plan for your music creation journey. Each song generation costs 100 credits.
      </p>
    </div>
  </section>
  <!-- Subscription Plans Section -->
  <section class="bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
      <div class="mx-auto max-w-screen-md text-center mb-8 lg:mb-12">
        <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">Subscription Plans</h2>
        <p class="mb-5 font-light text-gray-500 sm:text-xl dark:text-gray-400">Best value for regular music creators</p>
      </div>
      <div class="space-y-8 lg:grid lg:grid-cols-2 sm:gap-6 xl:gap-10 lg:space-y-0">
        <% @subscription_plans.each do |plan| %>
          <!-- Pricing Card -->
          <div class="flex flex-col p-6 mx-auto max-w-lg text-center text-gray-900 bg-white rounded-lg border border-gray-100 shadow dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white <%= 'relative' if plan.year? %>">
            <% if plan.year? %>
              <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <span class="bg-blue-100 text-blue-800 text-sm font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  <svg class="w-2.5 h-2.5 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
                  </svg>
                  Most Popular
                </span>
              </div>
            <% end %>
            <h3 class="mb-4 text-2xl font-semibold"><%= plan.name %></h3>
            <p class="font-light text-gray-500 sm:text-lg dark:text-gray-400"><%= plan.description %></p>
            <div class="flex justify-center items-baseline my-8">
              <span class="mr-2 text-5xl font-extrabold"><%= plan.formatted_amount %></span>
              <span class="text-gray-500 dark:text-gray-400">/<%= plan.billing_interval %></span>
            </div>
            <% if plan.year? %>
              <div class="flex justify-center mb-8">
                <span class="bg-green-100 text-green-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                  <svg class="w-2.5 h-2.5 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                  </svg>
                  Save ~16% vs Monthly
                </span>
              </div>
            <% end %>
            <!-- List -->
            <ul role="list" class="mb-8 space-y-4 text-left">
              <li class="flex items-center space-x-3">
                <svg class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5"/>
                </svg>
                <span><%= number_with_delimiter(plan.plan_limit) %> credits per month</span>
              </li>
              <li class="flex items-center space-x-3">
                <svg class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5"/>
                </svg>
                <span>Up to <%= plan.plan_limit / 100 %> songs per month</span>
              </li>
              <li class="flex items-center space-x-3">
                <svg class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5"/>
                </svg>
                <span>Credits reset monthly</span>
              </li>
              <li class="flex items-center space-x-3">
                <svg class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5"/>
                </svg>
                <span>Cancel anytime</span>
              </li>
            </ul>
            <% if authenticated? && Current.user.pro? && plan.subscription_plan? %>
              <% current_subscription_plan = Current.user.subscription&.plan %>
              <% if current_subscription_plan == plan %>
                <span class="text-white bg-gray-400 cursor-not-allowed font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                  Current Plan
                </span>
              <% else %>
                <span class="text-gray-500 bg-gray-200 cursor-not-allowed font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-700 dark:text-gray-400">
                  Plan Change Unavailable
                </span>
              <% end %>
            <% else %>
              <%= button_to "Get started", orders_path,
                  params: { plan_id: plan.id },
                  data: { turbo: false },
                  class: "text-white #{'bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-200' if plan.year?} #{'bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200' unless plan.year?} font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:text-white #{'dark:focus:ring-blue-900' if plan.year?} #{'dark:focus:ring-primary-900' unless plan.year?}" %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </section>
  <!-- Credit Packages Section -->
  <% if authenticated? && Current.user.pro? %>
    <section class="bg-gray-50 dark:bg-gray-800">
      <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
        <div class="mx-auto max-w-screen-md text-center mb-8 lg:mb-12">
          <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">Credit Packages</h2>
          <p class="mb-5 font-light text-gray-500 sm:text-xl dark:text-gray-400">One-time purchases for immediate use</p>
        </div>
        <div class="space-y-8 lg:grid lg:grid-cols-4 sm:gap-6 xl:gap-10 lg:space-y-0">
          <% @credit_packages.each_with_index do |plan, index| %>
            <!-- Credit Package Card -->
            <div class="flex flex-col p-6 mx-auto max-w-lg text-center text-gray-900 bg-white rounded-lg border border-gray-100 shadow dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white <%= 'relative' if index == 1 %>">
              <% if index == 1 %>
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <span class="bg-yellow-100 text-yellow-800 text-sm font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300">
                    <svg class="w-2.5 h-2.5 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                      <path d="m18.774 8.245-.892-.893a1.5 1.5 0 0 1-.437-1.052V5.036a2.484 2.484 0 0 0-2.48-2.48H13.7a1.5 1.5 0 0 1-1.052-.438l-.893-.892a2.484 2.484 0 0 0-3.51 0l-.893.892a1.5 1.5 0 0 1-1.052.437H5.036a2.484 2.484 0 0 0-2.48 2.481V6.3a1.5 1.5 0 0 1-.438 1.052l-.892.893a2.484 2.484 0 0 0 0 3.51l.892.893a1.5 1.5 0 0 1 .437 1.052v1.264a2.484 2.484 0 0 0 2.481 2.481H6.3a1.5 1.5 0 0 1 1.052.437l.893.892a2.484 2.484 0 0 0 3.51 0l.893-.892a1.5 1.5 0 0 1 1.052-.437h1.264a2.484 2.484 0 0 0 2.481-2.48V13.7a1.5 1.5 0 0 1 .437-1.052l.892-.893a2.484 2.484 0 0 0 0-3.51Z"/>
                      <path d="M8 13a1 1 0 0 1-.707-.293l-2-2a1 1 0 1 1 1.414-1.414l1.42 1.42 5.318-3.545a1 1 0 0 1 1.11 1.664l-6 4A1 1 0 0 1 8 13Z"/>
                    </svg>
                    Best Value
                  </span>
                </div>
              <% end %>
              <h3 class="mb-4 text-2xl font-semibold"><%= plan.name %></h3>
              <p class="font-light text-gray-500 sm:text-lg dark:text-gray-400"><%= plan.description %></p>
              <div class="flex justify-center items-baseline my-8">
                <span class="mr-2 text-5xl font-extrabold"><%= plan.formatted_amount %></span>
              </div>
              <div class="mb-4">
                <span class="bg-blue-100 text-blue-800 text-lg font-semibold inline-flex items-center px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                  <%= number_with_delimiter(plan.credits_amount) %> credits
                </span>
              </div>
              <p class="font-normal text-gray-500 dark:text-gray-400 mb-8">
                ~$<%= sprintf("%.2f", plan.unit_amount / 100.0 / (plan.credits_amount / 100.0)) %> per song
              </p>
              <%= button_to "Buy Now", orders_path,
                  params: { plan_id: plan.id },
                  data: { turbo: false },
                  class: "text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:text-white dark:focus:ring-primary-900" %>
            </div>
          <% end %>
        </div>
      </div>
    </section>
  <% end %>
  <!-- Bottom CTA for non-authenticated users -->
  <% unless authenticated? %>
    <section class="bg-white dark:bg-gray-900">
      <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
        <div class="mx-auto max-w-screen-sm text-center">
          <h2 class="mb-4 text-4xl tracking-tight font-extrabold leading-tight text-gray-900 dark:text-white">
            Ready to start creating music?
          </h2>
          <p class="mb-6 font-light text-gray-500 dark:text-gray-400 md:text-lg">
            Sign up for a free account to get started with 20 free credits!
          </p>
          <%= link_to new_session_path, 
              class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800 inline-flex items-center" do %>
            Get Started Free
            <svg class="w-3.5 h-3.5 ms-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
            </svg>
          <% end %>
        </div>
      </div>
    </section>
  <% end %>
</div>
