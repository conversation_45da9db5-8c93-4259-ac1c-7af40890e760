<ul role="list" class="divide-y divide-gray-200" id="songs_list">
  <!-- Generation Task Placeholders (2 per task) -->
  <% @generating_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/placeholder", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Failed Generation Tasks (2 per task) -->
  <% @failed_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/failed_list_item", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Actual Songs -->
  <% @songs.each do |song| %>
    <%= render "song_list_item", song: song %>
  <% end %>
  <!-- Empty State -->
  <% if @songs.empty? && @generating_tasks.empty? && @failed_tasks.empty? %>
    <li class="py-12 px-4">
      <div class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <%= flowbite_icon('music-outline', class: 'size-8 text-gray-400') %>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No songs yet</h3>
        <p class="text-gray-500 mb-6">Start creating your first AI-generated song</p>
        <%= link_to new_generation_path, 
              class: "inline-flex items-center px-4 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300" do %>
          <%= flowbite_icon('plus-outline', class: 'size-4 mr-2') %>
          Generate Music
        <% end %>
      </div>
    </li>
  <% end %>
</ul>