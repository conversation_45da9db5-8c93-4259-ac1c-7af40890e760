module SongsHelper
  # Generate a proper download filename for a song
  # Priority: song_title + extension from URL -> song_id + extension from URL -> song_id.mp3
  def download_filename_for_song(song)
    return nil unless song&.has_final_audio?

    # Extract extension from audio URL
    begin
      uri = URI.parse(song.audio_url)
      extension = File.extname(uri.path).presence || ".mp3"
    rescue URI::InvalidURIError
      extension = ".mp3"
    end

    # Use song title if available, otherwise fall back to song ID
    if song.title.present? && song.title != "Untitled Song"
      # Sanitize the title for filename use
      safe_title = song.title.gsub(/[^\w\s\-_\(\)]/, "").strip.gsub(/\s+/, "_")
      "#{safe_title}#{extension}"
    else
      # Fall back to song ID
      "#{song.id}#{extension}"
    end
  end

  def song_player_id(song)
    if song.has_final_audio?
      dom_id(song, :player)
    else
      song_stream_player_id(song)
    end
  end
  def song_stream_player_id(song)
    "#{dom_id(song, :player)}_stream"
  end

  # Generate DOM ID for song edit modal
  def song_edit_modal_id(song)
    dom_id(song, :edit_modal)
  end
end
