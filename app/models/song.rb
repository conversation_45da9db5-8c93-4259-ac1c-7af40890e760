class Song < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :generation_task

  # Validations
  validates :id, presence: true, uniqueness: true
  validates :audio_url, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }, allow_blank: true
  validates :stream_audio_url, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }, allow_blank: true
  validates :favorite, inclusion: { in: [ true, false ] }
  validates :metadata, presence: true
  validate :metadata_must_be_hash
  validate :lyrics_must_be_valid_hash
  validate :at_least_one_audio_url_present

  # Scopes
  scope :favorited, -> { where(favorite: true) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_user, ->(user) { where(user: user) }

  # Methods
  def toggle_favorite
    update(favorite: !favorite)
  end

  def update_title(new_title)
    return false if new_title.blank?

    # Validate title length
    if new_title.length > 100
      errors.add(:title, "is too long (maximum is 100 characters)")
      return false
    end

    # Update metadata with new title
    new_metadata = (metadata || {}).merge("title" => new_title.strip)
    update(metadata: new_metadata)
  end

  def title
    metadata&.dig("title") || "Untitled Song"
  end

  def duration
    metadata&.dig("duration")
  end

  def duration_formatted
    return nil unless duration
    minutes = duration / 60
    seconds = duration % 60
    format("%d:%02d", minutes, seconds)
  end

  def artist
    metadata&.dig("artist") || user.username
  end

  def genre
    metadata&.dig("genre")
  end

  def has_lyrics?
    lyrics_hash?
  end

  def lyrics_text
    return nil unless has_lyrics?

    case
    when lyrics_has_aligned_words?
      lyrics["text"]
    when lyrics["segments"].present?
      lyrics["segments"].map { |segment| segment["text"] }.join(" ")
    when lyrics["text"].present?
      lyrics["text"]
    end
  end

  def has_timestamped_lyrics?
    has_lyrics? && lyrics_has_aligned_words?
  end

  def lyrics_aligned_words
    return nil unless has_timestamped_lyrics?
    lyrics["aligned_words"]
  end

  def lyrics_waveform_data
    return nil unless has_timestamped_lyrics?
    lyrics["waveform_data"]
  end

  def has_stream_audio?
    stream_audio_url.present?
  end

  def has_final_audio?
    audio_url.present?
  end

  def generation_style
    generation_task&.generation_params&.dig("style")
  end

  def generation_mood
    generation_task&.generation_params&.dig("mood")
  end

  # 检查文件是否已转存到私人 bucket
  def audio_transferred?
    return false unless has_final_audio?
    audio_url.include?(cdn_url)
  end

  def image_transferred?
    image_url.include?(cdn_url)
  end

  def files_transferred?
    # Only check audio transfer if we have final audio
    audio_check = has_final_audio? ? audio_transferred? : true
    audio_check && (image_url.blank? || image_transferred?)
  end

  # Class methods for creation from API data
  def self.create_from_api_data!(task, song_data)
    audio_id = song_data["id"]
    return nil unless audio_id.present?

    # Check if song already exists
    existing_song = find_by(id: audio_id)
    if existing_song
      Rails.logger.info "📄 歌曲已存在，检查是否需要更新: #{audio_id}"

      if existing_song.should_update_from_api_data?(song_data)
        existing_song.update_from_api_data!(song_data, task)
      end

      return existing_song
    end

    # Extract audio URLs
    audio_url = song_data["audio_url"]
    stream_audio_url = song_data["stream_audio_url"]

    # Only create song if it has at least one audio URL (final or stream)
    unless (audio_url.present? && !audio_url.empty?) || (stream_audio_url.present? && !stream_audio_url.empty?)
      Rails.logger.info "📄 跳过创建歌曲，缺少音频URL: #{audio_id}"
      return nil
    end

    # Create new song
    song_attributes = {
      id: audio_id,
      user: task.user,
      generation_task: task,
      audio_url: audio_url.present? && !audio_url.empty? ? audio_url : nil,
      stream_audio_url: stream_audio_url.present? && !stream_audio_url.empty? ? stream_audio_url : nil,
      image_url: song_data["image_url"] || song_data["imageUrl"],
      metadata: extract_metadata_from_api_data(song_data),
      lyrics: extract_initial_lyrics_from_api_data(task, song_data)
    }

    song = create!(song_attributes)
    Rails.logger.info "✅ 歌曲创建成功: #{song.id}"

    # Schedule file transfer (always needed)
    song.schedule_file_transfer
    # Note: Lyrics generation will be scheduled only after task succeeds

    song
  rescue => e
    Rails.logger.error "❌ 创建歌曲失败: #{e.message}"
    Rails.logger.error "歌曲数据: #{song_data.inspect}"
    nil
  end

  # Instance methods for updating from API data
  def should_update_from_api_data?(song_data)
    # Extract audio URLs from new data
    audio_url = song_data["audio_url"]
    stream_audio_url = song_data["stream_audio_url"]

    # Check if existing song is missing audio URLs and new data has them
    existing_audio_missing = self.audio_url.blank?
    existing_stream_missing = self.stream_audio_url.blank?
    new_audio_present = audio_url.present? && !audio_url.empty?
    new_stream_present = stream_audio_url.present? && !stream_audio_url.empty?

    # Update if we have new audio data that we don't already have
    should_update = (existing_audio_missing && new_audio_present) ||
                   (existing_stream_missing && new_stream_present)

    Rails.logger.info "📄 检查歌曲更新需求: #{id}, 现有音频缺失: #{existing_audio_missing}, 现有流媒体缺失: #{existing_stream_missing}, 新音频存在: #{new_audio_present}, 新流媒体存在: #{new_stream_present}, 需要更新: #{should_update}"

    should_update
  end

  def update_from_api_data!(song_data, task)
    Rails.logger.info "🔄 更新现有歌曲: #{id}"

    update_attributes = {}

    # Update audio URLs if they were missing
    audio_url = song_data["audio_url"]
    stream_audio_url = song_data["stream_audio_url"]

    if audio_url.present? && !audio_url.empty? && self.audio_url.blank?
      update_attributes[:audio_url] = audio_url
    end

    if stream_audio_url.present? && !stream_audio_url.empty? && self.stream_audio_url.blank?
      update_attributes[:stream_audio_url] = stream_audio_url
    end

    # Update metadata with new information
    new_metadata = self.class.extract_metadata_from_api_data(song_data)
    if new_metadata.present?
      # Merge new metadata with existing, preferring new values
      merged_metadata = (metadata || {}).merge(new_metadata)
      update_attributes[:metadata] = merged_metadata
    end

    # Update the song if there are changes
    if update_attributes.any?
      update!(update_attributes)
      Rails.logger.info "✅ 歌曲更新成功: #{id}"

      # Schedule file transfer if final audio_url was added
      if update_attributes[:audio_url].present?
        schedule_file_transfer
        # Note: Lyrics generation will be scheduled only after task succeeds
      end
    end

    self
  rescue => e
    Rails.logger.error "❌ 更新歌曲失败: #{e.message}"
    Rails.logger.error "歌曲数据: #{song_data.inspect}"
    self
  end

  # Background job scheduling
  def schedule_file_transfer
    Rails.logger.info "📁 安排文件转存任务: 歌曲ID=#{id}"
    FileTransferJob.perform_later(id)
  end

  def schedule_lyrics_generation
    Rails.logger.info "🎤 安排歌词生成任务: 歌曲ID=#{id}, 任务ID=#{generation_task.task_id}"
    LyricsGenerationJob.perform_later(id, generation_task.task_id, id)
  end

  private

  # Class method for extracting metadata from API response
  def self.extract_metadata_from_api_data(song_data)
    {
      title: song_data["title"],
      duration: song_data["duration"],
      created_at: song_data["created_at"],
      model_name: song_data["model_name"],
      status: song_data["status"],
      prompt: song_data["prompt"],
      style: song_data["style"],
      type: song_data["type"]
    }.compact
  end

  # Class method for extracting initial lyrics from API response
  def self.extract_initial_lyrics_from_api_data(task, song_data)
    if task.instrumental?
      return { text: "" }
    end

    # 首先尝试从完成响应的 prompt 字段提取歌词
    lyrics_text = song_data["prompt"]

    if !lyrics_text.blank?
      return { text: lyrics_text }
    end

    lyrics_text = task.lyrics_from_params
    if !lyrics_text.blank?
      return { text: lyrics_text }
    end

    { text: "" }
  end

  def cdn_url
    # if missing, throw error
    # if not missing, return the url
    public_url = Rails.application.credentials.dig(:cloudflare_r2, :public_url)
    return public_url if public_url.present?

    raise "cloudflare_r2 public_url is missing"
  end

  # Helper methods for lyrics validation and extraction
  def lyrics_hash?
    lyrics.present? && lyrics.is_a?(Hash)
  end

  def lyrics_has_aligned_words?
    lyrics["aligned_words"].present?
  end

  def validate_field_type(field_name, value, expected_type, error_prefix = "lyrics")
    case expected_type
    when :string
      errors.add(:lyrics, "#{error_prefix} #{field_name} must be a string") unless value.is_a?(String)
    when :boolean
      errors.add(:lyrics, "#{error_prefix} #{field_name} must be a boolean") unless [ true, false ].include?(value)
    when :numeric
      errors.add(:lyrics, "#{error_prefix} #{field_name} must be a number") unless value.is_a?(Numeric)
    when :array
      errors.add(:lyrics, "#{error_prefix} #{field_name} must be an array") unless value.is_a?(Array)
    end
  end

  def validate_aligned_word(word, index)
    unless word.is_a?(Hash)
      errors.add(:lyrics, "aligned_words[#{index}] must be an object")
      return
    end

    # Check required fields
    required_fields = %w[word success startS endS]
    required_fields.each do |field|
      unless word.key?(field)
        errors.add(:lyrics, "aligned_words[#{index}] missing required field: #{field}")
      end
    end

    # Validate field types
    validate_field_type("word", word["word"], :string, "aligned_words[#{index}].") if word["word"].present?
    validate_field_type("success", word["success"], :boolean, "aligned_words[#{index}].") if word["success"].present?
    validate_field_type("startS", word["startS"], :numeric, "aligned_words[#{index}].") if word["startS"].present?
    validate_field_type("endS", word["endS"], :numeric, "aligned_words[#{index}].") if word["endS"].present?
  end

  def metadata_must_be_hash
    return if metadata.blank?

    unless metadata.is_a?(Hash)
      errors.add(:metadata, "must be a valid JSON hash")
    end
  end

  def lyrics_must_be_valid_hash
    return if lyrics.blank?

    unless lyrics_hash?
      errors.add(:lyrics, "must be a valid JSON hash")
      return
    end

    # Validate text field
    validate_field_type("text", lyrics["text"], :string) if lyrics["text"].present?

    # Validate aligned_words field
    if lyrics_has_aligned_words?
      validate_field_type("aligned_words", lyrics["aligned_words"], :array)

      if lyrics["aligned_words"].is_a?(Array)
        lyrics["aligned_words"].each_with_index { |word, index| validate_aligned_word(word, index) }
      end
    end

    # Validate waveform_data field
    if lyrics["waveform_data"].present?
      validate_field_type("waveform_data", lyrics["waveform_data"], :array)

      if lyrics["waveform_data"].is_a?(Array) && !lyrics["waveform_data"].all? { |item| item.is_a?(Numeric) }
        errors.add(:lyrics, "waveform_data must contain only numeric values")
      end
    end

    # Validate other fields using the helper
    validate_field_type("hoot_cer", lyrics["hoot_cer"], :numeric) if lyrics["hoot_cer"].present?
    validate_field_type("is_streamed", lyrics["is_streamed"], :boolean) if lyrics["is_streamed"].present?

    # Validate generated_at field
    if lyrics["generated_at"].present?
      begin
        Time.parse(lyrics["generated_at"].to_s)
      rescue ArgumentError
        errors.add(:lyrics, "generated_at field must be a valid timestamp")
      end
    end
  end

  def at_least_one_audio_url_present
    unless audio_url.present? || stream_audio_url.present?
      errors.add(:base, "Either audio_url or stream_audio_url must be present")
    end
  end
end
