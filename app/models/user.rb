class User < ApplicationRecord
  include PlanConfiguration

  # Associations
  has_many :sessions, dependent: :destroy
  has_many :identities, dependent: :destroy
  has_many :songs, dependent: :destroy
  has_many :generation_tasks, dependent: :destroy
  has_one :subscription, dependent: :destroy
  has_many :orders, dependent: :destroy

  # Enums
  enum :plan_type, { free: 0, pro: 1 }

  # Scopes
  scope :needs_credit_reset, -> { where("plan_credits_used > 0") }

  # Validations
  validates :username, presence: true, uniqueness: true
  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :plan_type, presence: true
  validates :extra_credits, :plan_credits_used, :plan_credits_limit,
            presence: true, numericality: { greater_than_or_equal_to: 0 }

  # Methods
  def spendable_credits
    available_plan_credits + extra_credits
  end

  def available_plan_credits
    [ plan_credits_limit - plan_credits_used, 0 ].max
  end

  # Check if user has sufficient credits for a given amount
  def has_sufficient_credits?(amount)
    spendable_credits >= amount
  end

  # Check if user has sufficient credits for generation
  def can_generate_music?
    has_sufficient_credits?(generation_cost)
  end

  # Spend credits logic: prioritize plan credits first, then extra credits
  def spend_credits!(amount)
    raise ArgumentError, "Amount must be positive" if amount <= 0
    raise InsufficientCreditsError, "Not enough credits" if spendable_credits < amount

    transaction do
      # First, spend available plan credits
      plan_to_spend = [ available_plan_credits, amount ].min
      if plan_to_spend > 0
        increment!(:plan_credits_used, plan_to_spend)
        amount -= plan_to_spend
      end

      # Then spend extra credits if needed
      if amount > 0
        decrement!(:extra_credits, amount)
      end
    end
  end

  def add_extra_credits!(amount)
    raise ArgumentError, "Amount must be positive" if amount <= 0
    increment!(:extra_credits, amount)
  end

  # Refund credits logic: prioritize plan credits first, then extra credits
  def refund_credits!(amount)
    raise ArgumentError, "Amount must be positive" if amount <= 0

    transaction do
      # First, refund to plan credits (reduce plan_credits_used)
      plan_refund_amount = [ amount, plan_credits_used ].min
      if plan_refund_amount > 0
        decrement!(:plan_credits_used, plan_refund_amount)
        amount -= plan_refund_amount
      end

      # Then add remaining amount to extra credits
      if amount > 0
        increment!(:extra_credits, amount)
      end
    end
  end

  def reset_plan_credits!
    update!(plan_credits_used: 0)
  end

  # Check if Pro user needs credit reset based on subscription period
  def needs_pro_credit_reset?
    return false unless pro? && subscription&.active?

    # Calculate next reset date based on subscription
    next_reset = next_credit_reset_date
    return false unless next_reset

    # Need reset if current time is past the next reset date
    Time.current >= next_reset
  end

  # Calculate next credit reset date for Pro users
  def next_credit_reset_date
    return nil unless pro? && subscription&.active?

    plan = subscription.plan
    return nil unless plan&.plan_limit_reset_interval

    # Get the last reset date (either subscription start or last manual reset)
    last_reset = subscription.current_period_start || subscription.created_at

    case plan.plan_limit_reset_interval
    when "month"
      # Monthly reset - add one month to last reset
      last_reset + 1.month
    when "year"
      # Yearly reset - add one year to last reset
      last_reset + 1.year
    else
      nil
    end
  end

  def stripe_customer
    find_or_create_stripe_customer!
  end

  def find_or_create_stripe_customer!
    # Try to find existing Stripe customer by email using search API
    search_results = Stripe::Customer.search({
      query: "email:'#{email}'"
    })

    # Return the first customer if found
    return search_results.data.first if search_results.data.any?

    # Create new Stripe customer using user's email
    stripe_customer = Stripe::Customer.create(
      email: email,
      name: username,
      metadata: {
        user_id: id,
        username: username
      }
    )

    stripe_customer
  end

  # Class methods
  def self.find_or_create_from_email_otp(email)
    # Try to find existing user via identity
    identity = Identity.find_by(provider: "email_otp", uid: email)
    if identity&.user
      # Update user email if it's different (in case user changed email)
      user = identity.user
      user.update!(email: email) if user.email != email
      return [ user, false ]
    end

    user = nil
    transaction do
      user = create!(
        username: generate_username_from_email(email),
        email: email,
        plan_type: :free,
        plan_credits_limit: free_plan_credits_limit
      )
      user.identities.create!(
        provider: "email_otp",
        uid: email,
        email: email
      )
    end
    [ user, true ]
  end

  def self.from_omniauth(auth_hash)
    identity = Identity.find_by(provider: auth_hash.provider, uid: auth_hash.uid)
    if identity&.user
      # Update user email if it's different (in case user changed email)
      user = identity.user
      user.update!(email: auth_hash.info.email) if user.email != auth_hash.info.email
      return [ user, false ]
    end

    user = nil
    transaction do
      user = create!(
        username: generate_username_from_auth_hash(auth_hash),
        email: auth_hash.info.email,
        plan_type: :free,
        plan_credits_limit: free_plan_credits_limit
      )
      user.identities.create!(
        provider: auth_hash.provider,
        uid: auth_hash.uid,
        email: auth_hash.info.email
      )
    end
    [ user, true ]
  end

  # Generates a username from an auth hash, falling back from name to email, then to random
  def self.generate_username_from_auth_hash(auth_hash)
    info = auth_hash["info"] || auth_hash[:info] || {}
    name = info["name"] || info[:name]
    email = info["email"] || info[:email]

    if name.present?
      username_base = name.parameterize(separator: "_")
      username = username_base
      counter = 1
      while User.exists?(username: username)
        username = "#{username_base}#{counter}"
        counter += 1
      end
      username
    elsif email.present?
      generate_username_from_email(email)
    else
      # Fallback to random username if neither name nor email is present
      "user_#{SecureRandom.hex(3)}"
    end
  end

  private

  def self.generate_username_from_email(email)
    username_base = email.split("@").first
    username = username_base
    counter = 1
    while User.exists?(username: username)
      username = "#{username_base}#{counter}"
      counter += 1
    end
    username
  end

  def self.generate_random_username(email)
    base = email.split("@").first
    "#{base}_#{SecureRandom.hex(3)}"
  end
end

# Custom exception for insufficient credits
class InsufficientCreditsError < StandardError; end
